<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.BpmIdMappingMapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.BpmIdMapping">
    <!--@mbg.generated-->
    <!--@Table mm_bpm_id_mapping-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bpm_id" jdbcType="VARCHAR" property="bpmId" />
    <result column="pbm_name" jdbcType="VARCHAR" property="pbmName" />
    <result column="customize" jdbcType="VARCHAR" property="customize" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, bpm_id, pbm_name, customize, update_time, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from mm_bpm_id_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from mm_bpm_id_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.BpmIdMapping" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into mm_bpm_id_mapping (bpm_id, pbm_name, customize, 
      update_time, remark)
    values (#{bpmId,jdbcType=VARCHAR}, #{pbmName,jdbcType=VARCHAR}, #{customize,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.BpmIdMapping" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into mm_bpm_id_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bpmId != null and bpmId != ''">
        bpm_id,
      </if>
      <if test="pbmName != null and pbmName != ''">
        pbm_name,
      </if>
      <if test="customize != null and customize != ''">
        customize,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="remark != null and remark != ''">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bpmId != null and bpmId != ''">
        #{bpmId,jdbcType=VARCHAR},
      </if>
      <if test="pbmName != null and pbmName != ''">
        #{pbmName,jdbcType=VARCHAR},
      </if>
      <if test="customize != null and customize != ''">
        #{customize,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null and remark != ''">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.BpmIdMapping">
    <!--@mbg.generated-->
    update mm_bpm_id_mapping
    <set>
      <if test="bpmId != null and bpmId != ''">
        bpm_id = #{bpmId,jdbcType=VARCHAR},
      </if>
      <if test="pbmName != null and pbmName != ''">
        pbm_name = #{pbmName,jdbcType=VARCHAR},
      </if>
      <if test="customize != null and customize != ''">
        customize = #{customize,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.BpmIdMapping">
    <!--@mbg.generated-->
    update mm_bpm_id_mapping
    set bpm_id = #{bpmId,jdbcType=VARCHAR},
      pbm_name = #{pbmName,jdbcType=VARCHAR},
      customize = #{customize,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update mm_bpm_id_mapping
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="bpm_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.bpmId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="pbm_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.pbmName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="customize = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.customize,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update mm_bpm_id_mapping
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="bpm_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bpmId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.bpmId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="pbm_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pbmName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.pbmName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="customize = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.customize != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.customize,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into mm_bpm_id_mapping
    (bpm_id, pbm_name, customize, update_time, remark)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bpmId,jdbcType=VARCHAR}, #{item.pbmName,jdbcType=VARCHAR}, #{item.customize,jdbcType=VARCHAR},
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.remark,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <!-- 分页查询 -->
  <select id="listByPage" parameterType="com.pttl.mobile.manager.domain.request.BpmIdMappingPageRequest" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mm_bpm_id_mapping
    <where>
      <if test="bpmId != null and bpmId != ''">
        and bpm_id like concat('%', #{bpmId}, '%')
      </if>
      <if test="pbmName != null and pbmName != ''">
        and pbm_name like concat('%', #{pbmName}, '%')
      </if>
      <if test="customize != null and customize != ''">
        and customize like concat('%', #{customize}, '%')
      </if>
      <if test="remark != null and remark != ''">
        and remark like concat('%', #{remark}, '%')
      </if>
    </where>
    order by update_time desc, id desc
  </select>

  <!-- 批量删除 -->
  <delete id="deleteByIds" parameterType="java.util.List">
    delete from mm_bpm_id_mapping
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <!-- 查询所有数据（用于导出） -->
  <select id="listAll" parameterType="com.pttl.mobile.manager.domain.request.BpmIdMappingPageRequest" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mm_bpm_id_mapping
    <where>
      <if test="bpmId != null and bpmId != ''">
        and bpm_id like concat('%', #{bpmId}, '%')
      </if>
      <if test="pbmName != null and pbmName != ''">
        and pbm_name like concat('%', #{pbmName}, '%')
      </if>
      <if test="customize != null and customize != ''">
        and customize like concat('%', #{customize}, '%')
      </if>
      <if test="remark != null and remark != ''">
        and remark like concat('%', #{remark}, '%')
      </if>
    </where>
    order by update_time desc, id desc
  </select>

  <!-- 根据bmpId查询单条记录 -->
  <select id="selectByBmpId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mm_bpm_id_mapping
    where bmp_id = #{bmpId,jdbcType=VARCHAR}
  </select>
</mapper>