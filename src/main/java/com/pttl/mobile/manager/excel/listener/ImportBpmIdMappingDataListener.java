package com.pttl.mobile.manager.excel.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.pttl.mobile.manager.domain.entity.BpmIdMapping;
import com.pttl.mobile.manager.excel.dto.ImportBpmIdMappingDataDTO;
import com.pttl.mobile.manager.excel.exception.ExcelErrorResultException;
import com.pttl.mobile.manager.service.BpmIdMappingService;
import com.pttl.mobile.manager.util.BeanMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

/**
 * BpmIdMapping数据导入监听器
 *
 * <AUTHOR>
 * @date 2025/6/17
 */
@Slf4j
public class ImportBpmIdMappingDataListener extends AnalysisEventListener<ImportBpmIdMappingDataDTO> {

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;

    /**
     * 缓存的数据
     */
    private List<ImportBpmIdMappingDataDTO> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    /**
     * 业务服务
     */
    private final BpmIdMappingService bpmIdMappingService;

    public ImportBpmIdMappingDataListener(BpmIdMappingService bpmIdMappingService) {
        this.bpmIdMappingService = bpmIdMappingService;
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context analysis context
     */
    @Override
    public void invoke(ImportBpmIdMappingDataDTO data, AnalysisContext context) {
        log.info("解析到一条数据:{}", data);
        
        // 数据校验
        validateData(data, context.readRowHolder().getRowIndex());
        
        cachedDataList.add(data);
        
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (cachedDataList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context analysis context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        log.info("所有数据解析完成！");
    }

    /**
     * 数据校验
     *
     * @param data     数据
     * @param rowIndex 行号
     */
    private void validateData(ImportBpmIdMappingDataDTO data, Integer rowIndex) {
        if (StrUtil.isBlank(data.getBpmId())) {
            throw new ExcelErrorResultException("第" + (rowIndex + 1) + "行：BPM ID不能为空", ListUtils.newArrayListWithExpectedSize(1));
        }
        
        if (StrUtil.isBlank(data.getPbmName())) {
            throw new ExcelErrorResultException("第" + (rowIndex + 1) + "行：BPM名称不能为空", ListUtils.newArrayListWithExpectedSize(1));
        }
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("{}条数据，开始存储数据库！", cachedDataList.size());
        
        // 转换为实体对象
        List<BpmIdMapping> bpmIdMappingList = BeanMapper.mapList(cachedDataList, BpmIdMapping.class);
        
        // 设置更新时间
        Date now = new Date();
        bpmIdMappingList.forEach(item -> item.setUpdateTime(now));
        
        // 批量插入
        bpmIdMappingService.batchInsert(bpmIdMappingList);
        
        log.info("存储数据库成功！");
    }
}
