package com.pttl.mobile.manager.excel.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 导出BpmIdMapping数据DTO
 *
 * <AUTHOR>
 * @date 2025/6/17
 */
@Data
public class ExportBpmIdMappingDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @ExcelProperty(value = "ID", index = 0)
    private Long id;

    /**
     * bpm id
     */
    @ApiModelProperty(value = "BPM ID")
    @ExcelProperty(value = "BPM ID", index = 1)
    private String bpmId;

    /**
     * bpm名称
     */
    @ApiModelProperty(value = "BPM名称")
    @ExcelProperty(value = "BPM名称", index = 2)
    private String pbmName;

    /**
     * 自定义id
     */
    @ApiModelProperty(value = "自定义ID")
    @ExcelProperty(value = "自定义ID", index = 3)
    private String customize;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @ExcelProperty(value = "更新时间", index = 4)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注", index = 5)
    private String remark;
}
