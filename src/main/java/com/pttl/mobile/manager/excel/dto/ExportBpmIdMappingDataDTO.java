package com.pttl.mobile.manager.excel.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 导出BpmIdMapping数据DTO
 *
 * <AUTHOR>
 * @date 2025/6/17
 */
@Data
public class ExportBpmIdMappingDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @ExcelProperty(value = "ID", index = 0)
    private Long id;

    /**
     * bpm id
     */
    @ApiModelProperty(value = "BPM ID")
    @ExcelProperty(value = "BPM ID", index = 1)
    private String bpmId;

    /**
     * bpm名称
     */
    @ApiModelProperty(value = "BPM名称")
    @ExcelProperty(value = "BPM名称", index = 2)
    private String pbmName;

    /**
     * 自定义id
     */
    @ApiModelProperty(value = "自定义ID")
    @ExcelProperty(value = "自定义ID", index = 3)
    private String customize;
}
