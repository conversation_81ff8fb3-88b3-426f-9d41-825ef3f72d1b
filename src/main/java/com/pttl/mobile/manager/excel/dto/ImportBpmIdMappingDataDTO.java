package com.pttl.mobile.manager.excel.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 导入BpmIdMapping数据DTO
 *
 * <AUTHOR>
 * @date 2025/6/17
 */
@Data
public class ImportBpmIdMappingDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * bpm id
     */
    @ApiModelProperty(value = "BPM ID(必填)")
    @ExcelProperty(value = "BPM ID(必填)", index = 0)
    private String bpmId;

    /**
     * bpm名称
     */
    @ApiModelProperty(value = "BPM名称(必填)")
    @ExcelProperty(value = "BPM名称(必填)", index = 1)
    private String pbmName;

    /**
     * 自定义id
     */
    @ApiModelProperty(value = "自定义ID")
    @ExcelProperty(value = "自定义ID", index = 2)
    private String customize;
}
