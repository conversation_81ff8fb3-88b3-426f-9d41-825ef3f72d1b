package com.pttl.mobile.manager.util;

import com.alibaba.excel.EasyExcelFactory;
import com.pttl.mobile.manager.excel.dto.ImportBpmIdMappingDataDTO;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * BpmIdMapping Excel模板生成工具
 *
 * <AUTHOR>
 * @date 2025/6/17
 */
public class BpmIdMappingTemplateGenerator {

    /**
     * 生成BpmIdMapping导入模板
     *
     * @param filePath 文件路径
     */
    public static void generateTemplate(String filePath) {
        // 创建示例数据
        List<ImportBpmIdMappingDataDTO> templateData = new ArrayList<>();
        
        ImportBpmIdMappingDataDTO example1 = new ImportBpmIdMappingDataDTO();
        example1.setBpmId("BPM001");
        example1.setPbmName("示例BPM流程1");
        example1.setCustomize("CUSTOM001");
        example1.setRemark("这是一个示例数据，请按照此格式填写");
        templateData.add(example1);
        
        ImportBpmIdMappingDataDTO example2 = new ImportBpmIdMappingDataDTO();
        example2.setBpmId("BPM002");
        example2.setPbmName("示例BPM流程2");
        example2.setCustomize("CUSTOM002");
        example2.setRemark("BPM ID和BPM名称为必填项");
        templateData.add(example2);

        // 确保目录存在
        File file = new File(filePath);
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }

        try {
            // 生成Excel文件
            EasyExcelFactory.write(filePath, ImportBpmIdMappingDataDTO.class)
                    .sheet("BPM映射导入模板")
                    .doWrite(templateData);
            System.out.println("BPM映射导入模板已生成：" + filePath);
        } catch (Exception e) {
            System.err.println("生成模板失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        // 生成模板文件
        String templatePath = "src/main/resources/template/bpmIdMappingTemplate.xlsx";
        generateTemplate(templatePath);
    }
}
