package com.pttl.mobile.manager.util;

import com.alibaba.excel.EasyExcelFactory;
import com.pttl.mobile.manager.excel.dto.ImportBpmIdMappingDataDTO;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel模板生成工具
 *
 * <AUTHOR>
 * @date 2025/6/17
 */
public class ExcelTemplateGenerator {

    /**
     * 生成BpmIdMapping导入模板
     *
     * @param filePath 文件路径
     */
    public static void generateBpmIdMappingTemplate(String filePath) {
        // 创建示例数据
        List<ImportBpmIdMappingDataDTO> templateData = new ArrayList<>();
        
        ImportBpmIdMappingDataDTO example1 = new ImportBpmIdMappingDataDTO();
        example1.setBpmId("BPM001");
        example1.setPbmName("示例BPM流程1");
        example1.setCustomize("CUSTOM001");
        example1.setRemark("这是一个示例数据");
        templateData.add(example1);
        
        ImportBpmIdMappingDataDTO example2 = new ImportBpmIdMappingDataDTO();
        example2.setBpmId("BPM002");
        example2.setPbmName("示例BPM流程2");
        example2.setCustomize("CUSTOM002");
        example2.setRemark("请按照此格式填写数据");
        templateData.add(example2);

        // 确保目录存在
        File file = new File(filePath);
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }

        // 生成Excel文件
        EasyExcelFactory.write(filePath, ImportBpmIdMappingDataDTO.class)
                .sheet("BPM映射导入模板")
                .doWrite(templateData);
    }

    public static void main(String[] args) {
        // 生成模板文件
        String templatePath = "src/main/resources/templates/bpmIdMappingTemplate.xlsx";
        generateBpmIdMappingTemplate(templatePath);
        System.out.println("BPM映射导入模板已生成：" + templatePath);
    }
}
