package com.pttl.mobile.manager.service.impl;

import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import com.pttl.mobile.manager.dao.BpmIdMappingMapper;
import com.pttl.mobile.manager.domain.entity.BpmIdMapping;
import com.pttl.mobile.manager.service.BpmIdMappingService;
@Service
public class BpmIdMappingServiceImpl implements BpmIdMappingService{

    @Autowired
    private BpmIdMappingMapper bpmIdMappingMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return bpmIdMappingMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(BpmIdMapping record) {
        return bpmIdMappingMapper.insert(record);
    }

    @Override
    public int insertSelective(BpmIdMapping record) {
        return bpmIdMappingMapper.insertSelective(record);
    }

    @Override
    public BpmIdMapping selectByPrimaryKey(Long id) {
        return bpmIdMappingMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(BpmIdMapping record) {
        return bpmIdMappingMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(BpmIdMapping record) {
        return bpmIdMappingMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<BpmIdMapping> list) {
        return bpmIdMappingMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<BpmIdMapping> list) {
        return bpmIdMappingMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<BpmIdMapping> list) {
        return bpmIdMappingMapper.batchInsert(list);
    }

}
