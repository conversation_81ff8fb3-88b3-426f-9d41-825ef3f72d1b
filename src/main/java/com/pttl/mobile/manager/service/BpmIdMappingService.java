package com.pttl.mobile.manager.service;

import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.BpmIdMappingPageDTO;
import com.pttl.mobile.manager.domain.entity.BpmIdMapping;
import com.pttl.mobile.manager.domain.request.BpmIdMappingPageRequest;

import java.util.List;

public interface BpmIdMappingService{

    int deleteByPrimaryKey(Long id);

    int insert(BpmIdMapping record);

    int insertSelective(BpmIdMapping record);

    BpmIdMapping selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BpmIdMapping record);

    int updateByPrimaryKey(BpmIdMapping record);

    int updateBatch(List<BpmIdMapping> list);

    int updateBatchSelective(List<BpmIdMapping> list);

    int batchInsert(List<BpmIdMapping> list);

    /**
     * 分页查询
     *
     * @param pageRequest 分页查询参数
     * @return 分页结果
     */
    PageInfo<BpmIdMappingPageDTO> listByPage(BpmIdMappingPageRequest pageRequest);

    /**
     * 批量删除
     *
     * @param ids 主键ID列表
     * @return 删除数量
     */
    int deleteByIds(List<Long> ids);

    /**
     * 查询所有数据（用于导出）
     *
     * @param pageRequest 查询条件
     * @return 数据列表
     */
    List<BpmIdMapping> listAll(BpmIdMappingPageRequest pageRequest);

}
