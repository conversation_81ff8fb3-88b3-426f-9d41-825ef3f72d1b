package com.pttl.mobile.manager.service;

import java.util.List;
import com.pttl.mobile.manager.domain.entity.BpmIdMapping;
public interface BpmIdMappingService{

    int deleteByPrimaryKey(Long id);

    int insert(BpmIdMapping record);

    int insertSelective(BpmIdMapping record);

    BpmIdMapping selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BpmIdMapping record);

    int updateByPrimaryKey(BpmIdMapping record);

    int updateBatch(List<BpmIdMapping> list);

    int updateBatchSelective(List<BpmIdMapping> list);

    int batchInsert(List<BpmIdMapping> list);

}
