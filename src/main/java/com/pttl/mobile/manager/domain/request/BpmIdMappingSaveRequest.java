package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * BpmIdMapping保存请求
 *
 * <AUTHOR>
 * @date 2025/6/17
 */
@Data
@ApiModel(value = "BpmIdMapping保存请求")
public class BpmIdMappingSaveRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * bpm id
     */
    @ApiModelProperty(value = "BPM ID", required = true)
    private String bpmId;

    /**
     * bpm名称
     */
    @ApiModelProperty(value = "BPM名称", required = true)
    private String pbmName;

    /**
     * 自定义id
     */
    @ApiModelProperty(value = "自定义ID")
    private String customize;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
