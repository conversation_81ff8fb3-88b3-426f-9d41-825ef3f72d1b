package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * BpmIdMapping分页查询请求
 *
 * <AUTHOR>
 * @date 2025/6/17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BpmIdMapping分页查询请求")
public class BpmIdMappingPageRequest extends BasePageRequest {

    /**
     * bpm id
     */
    @ApiModelProperty(value = "BPM ID")
    private String bpmId;

    /**
     * bpm名称
     */
    @ApiModelProperty(value = "BPM名称")
    private String pbmName;

    /**
     * 自定义id
     */
    @ApiModelProperty(value = "自定义ID")
    private String customize;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
