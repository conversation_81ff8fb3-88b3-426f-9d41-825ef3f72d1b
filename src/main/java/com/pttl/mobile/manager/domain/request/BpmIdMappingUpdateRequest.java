package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * BpmIdMapping更新请求
 *
 * <AUTHOR>
 * @date 2025/6/17
 */
@Data
@ApiModel(value = "BpmIdMapping更新请求")
public class BpmIdMappingUpdateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Long id;

    /**
     * bpm id
     */
    @ApiModelProperty(value = "BPM ID", required = true)
    private String bpmId;

    /**
     * bpm名称
     */
    @ApiModelProperty(value = "BPM名称", required = true)
    private String pbmName;

    /**
     * 自定义id
     */
    @ApiModelProperty(value = "自定义ID")
    private String customize;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
