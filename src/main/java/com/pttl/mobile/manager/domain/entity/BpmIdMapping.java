package com.pttl.mobile.manager.domain.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class BpmIdMapping implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
    * bpm id
    */
    private String bpmId;

    /**
    * bpm名称
    */
    private String pbmName;

    /**
    * 自定义id
    */
    private String customize;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 备注
    */
    private String remark;
}