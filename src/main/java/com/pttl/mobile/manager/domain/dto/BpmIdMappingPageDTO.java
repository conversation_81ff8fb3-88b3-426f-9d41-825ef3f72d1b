package com.pttl.mobile.manager.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * BpmIdMapping分页查询结果DTO
 *
 * <AUTHOR>
 * @date 2025/6/17
 */
@Data
@ApiModel(value = "BpmIdMapping分页查询结果")
public class BpmIdMappingPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * bpm id
     */
    @ApiModelProperty(value = "BPM ID")
    private String bpmId;

    /**
     * bpm名称
     */
    @ApiModelProperty(value = "BPM名称")
    private String pbmName;

    /**
     * 自定义id
     */
    @ApiModelProperty(value = "自定义ID")
    private String customize;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
