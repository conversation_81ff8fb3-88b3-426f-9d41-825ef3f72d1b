package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.entity.BpmIdMapping;
import com.pttl.mobile.manager.domain.request.BpmIdMappingPageRequest;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface BpmIdMappingMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(BpmIdMapping record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(BpmIdMapping record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    BpmIdMapping selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BpmIdMapping record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BpmIdMapping record);

    int updateBatch(@Param("list") List<BpmIdMapping> list);

    int updateBatchSelective(@Param("list") List<BpmIdMapping> list);

    int batchInsert(@Param("list") List<BpmIdMapping> list);

    /**
     * 分页查询
     * @param pageRequest 分页查询参数
     * @return 查询结果
     */
    List<BpmIdMapping> listByPage(BpmIdMappingPageRequest pageRequest);

    /**
     * 批量删除
     * @param ids 主键ID列表
     * @return 删除数量
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 查询所有数据（用于导出）
     * @param pageRequest 查询条件
     * @return 数据列表
     */
    List<BpmIdMapping> listAll(BpmIdMappingPageRequest pageRequest);

    /**
     * 根据bmpId查询单条记录
     * @param bmpId BMP ID
     * @return 查询结果
     */
    BpmIdMapping selectByBmpId(@Param("bmpId") String bmpId);
}