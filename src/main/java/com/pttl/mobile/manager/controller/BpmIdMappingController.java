package com.pttl.mobile.manager.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.BpmIdMappingPageDTO;
import com.pttl.mobile.manager.domain.entity.BpmIdMapping;
import com.pttl.mobile.manager.domain.request.BpmIdMappingPageRequest;
import com.pttl.mobile.manager.domain.request.BpmIdMappingSaveRequest;
import com.pttl.mobile.manager.domain.request.BpmIdMappingUpdateRequest;
import com.pttl.mobile.manager.excel.dto.ExportBpmIdMappingDataDTO;
import com.pttl.mobile.manager.excel.dto.ImportBpmIdMappingDataDTO;
import com.pttl.mobile.manager.excel.exception.ExcelErrorResultException;
import com.pttl.mobile.manager.excel.listener.ImportBpmIdMappingDataListener;
import com.pttl.mobile.manager.excel.util.ExportExcelFileUtil;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.BpmIdMappingService;
import com.pttl.mobile.manager.util.BeanMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * BPM ID映射控制层
 *
 * <AUTHOR>
 * @date 2025/6/17
 */
@Slf4j
@RestController
@RequestMapping("/bpmIdMapping")
@Api(tags = "BPM ID映射管理")
@Validated
public class BpmIdMappingController {

    @Autowired
    private BpmIdMappingService bpmIdMappingService;

    @Value("${excelFile.bpmIdMappingTemplatePath:/home/<USER>/xiBel/manager/bpmIdMappingTemplate.xlsx}")
    private String excelTemplatePath;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询BPM映射", notes = "根据ID查询BPM映射详情")
    public ResponseMessage<BpmIdMapping> selectOne(@PathVariable("id") Long id) {
        BpmIdMapping result = bpmIdMappingService.selectByPrimaryKey(id);
        return ResponseMessage.ok(result);
    }

    /**
     * 分页查询
     *
     * @param pageRequest 分页查询参数
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询BPM映射", notes = "分页查询BPM映射列表")
    public ResponseMessage<PageInfo<BpmIdMappingPageDTO>> listByPage(@RequestBody BpmIdMappingPageRequest pageRequest) {
        PageInfo<BpmIdMappingPageDTO> result = bpmIdMappingService.listByPage(pageRequest);
        return ResponseMessage.ok(result);
    }

    /**
     * 新增BPM映射
     *
     * @param saveRequest 保存请求
     * @return 操作结果
     */
    @PostMapping
    @ApiOperation(value = "新增BPM映射", notes = "新增BPM映射记录")
    public ResponseMessage<Boolean> save(@Valid @RequestBody BpmIdMappingSaveRequest saveRequest) {
        BpmIdMapping bpmIdMapping = BeanMapper.map(saveRequest, BpmIdMapping.class);
        bpmIdMapping.setUpdateTime(new Date());
        int result = bpmIdMappingService.insertSelective(bpmIdMapping);
        return ResponseMessage.ok(result > 0);
    }

    /**
     * 更新BPM映射
     *
     * @param updateRequest 更新请求
     * @return 操作结果
     */
    @PutMapping
    @ApiOperation(value = "更新BPM映射", notes = "更新BPM映射记录")
    public ResponseMessage<Boolean> update(@Valid @RequestBody BpmIdMappingUpdateRequest updateRequest) {
        BpmIdMapping bpmIdMapping = BeanMapper.map(updateRequest, BpmIdMapping.class);
        bpmIdMapping.setUpdateTime(new Date());
        int result = bpmIdMappingService.updateByPrimaryKeySelective(bpmIdMapping);
        return ResponseMessage.ok(result > 0);
    }

    /**
     * 删除BPM映射
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除BPM映射", notes = "根据ID删除BPM映射")
    public ResponseMessage<Boolean> delete(@PathVariable("id") Long id) {
        int result = bpmIdMappingService.deleteByPrimaryKey(id);
        return ResponseMessage.ok(result > 0);
    }

    /**
     * 批量删除BPM映射
     *
     * @param ids 主键ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除BPM映射", notes = "批量删除BPM映射记录")
    public ResponseMessage<Boolean> deleteBatch(@RequestBody List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return ResponseMessage.error("请选择要删除的记录");
        }
        int result = bpmIdMappingService.deleteByIds(ids);
        return ResponseMessage.ok(result > 0);
    }

    /**
     * 导出BPM映射数据
     *
     * @param pageRequest 查询条件
     * @param response    响应对象
     * @throws IOException IO异常
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出BPM映射数据", notes = "根据查询条件导出BPM映射数据到Excel")
    public void exportData(@RequestBody BpmIdMappingPageRequest pageRequest, HttpServletResponse response) throws IOException {
        // 查询所有符合条件的数据
        List<BpmIdMapping> dataList = bpmIdMappingService.listAll(pageRequest);

        // 转换为导出DTO
        List<ExportBpmIdMappingDataDTO> exportList = BeanMapper.mapList(dataList, ExportBpmIdMappingDataDTO.class);

        // 导出Excel
        ExportExcelFileUtil.dataExport("BPM映射数据", "BPM映射列表", exportList, ExportBpmIdMappingDataDTO.class, response);
    }

    /**
     * 下载导入模板
     *
     * @param response 响应对象
     */
    @GetMapping("/downloadTemplate")
    @ApiOperation(value = "下载BPM映射导入模板", notes = "下载BPM映射数据导入模板")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("BPM映射导入模板", "UTF-8").replace("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 创建空的模板数据（只有表头）
            List<ImportBpmIdMappingDataDTO> templateData = new ArrayList<>();

            // 生成Excel模板文件
            EasyExcelFactory.write(response.getOutputStream(), ImportBpmIdMappingDataDTO.class)
                    .sheet("BPM映射导入模板")
                    .doWrite(templateData);
        } catch (Exception e) {
            log.error("下载模板失败", e);
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            ResponseMessage<Object> responseMessage = ResponseMessage.error("下载模板失败");
            response.getWriter().println(JSON.toJSONString(responseMessage));
        }
    }

    /**
     * 导入BPM映射数据
     *
     * @param file 上传的Excel文件
     * @return 导入结果
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入BPM映射数据", notes = "从Excel文件导入BPM映射数据，支持xls/xlsx/csv格式")
    public ResponseMessage<Object> importData(@RequestParam(name = "file") MultipartFile file) {
        // 判空
        if (Objects.isNull(file)) {
            return ResponseMessage.error("请选择文件");
        }

        log.info("开始导入BPM映射数据，文件名: {}", file.getOriginalFilename());

        try {
            // 文件流
            ExcelReaderBuilder excelReaderBuilder = EasyExcelFactory.read(file.getInputStream(),
                    // 实体类
                    ImportBpmIdMappingDataDTO.class,
                    // 监听器
                    new ImportBpmIdMappingDataListener(bpmIdMappingService));

            // 3.0版本开始 兼容csv 默认只支持xls/xlsx
            if (file.getOriginalFilename().contains(ExcelTypeEnum.CSV.getValue())) {
                excelReaderBuilder.excelType(ExcelTypeEnum.CSV);
            }

            excelReaderBuilder.sheet().doRead();
        } catch (IOException ioException) {
            log.error("文件解析错误", ioException);
            return ResponseMessage.error("文件解析错误");
        } catch (ExcelErrorResultException excelErrorResultException) {
            log.error("Excel数据错误", excelErrorResultException);
            return new ResponseMessage<>(ResponseMessage.ERROR, excelErrorResultException.getMessage(),
                    excelErrorResultException.getObject());
        } catch (Exception e) {
            log.error("导入数据异常", e);
            return ResponseMessage.error("导入数据失败：" + e.getMessage());
        }

        return ResponseMessage.ok("导入成功");
    }
}
