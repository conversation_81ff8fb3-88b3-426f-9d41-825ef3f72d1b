package com.pttl.mobile.manager.controller;
import com.pttl.mobile.manager.domain.entity.BpmIdMapping;
import com.pttl.mobile.manager.service.impl.BpmIdMappingServiceImpl;
import org.springframework.web.bind.annotation.*;

import org.springframework.beans.factory.annotation.Autowired;

/**
* bpm id映射控制层
*
 * <AUTHOR>
 */
@RestController
@RequestMapping("/bpmIdMapping")
public class BpmIdMappingController {

    /**
    * 服务对象
    */
    @Autowired
    private BpmIdMappingServiceImpl bpmIdMappingServiceImpl;

    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @GetMapping("selectOne")
    public BpmIdMapping selectOne(Long id) {
        return bpmIdMappingServiceImpl.selectByPrimaryKey(id);
    }


}
