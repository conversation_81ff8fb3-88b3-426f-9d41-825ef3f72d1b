package com.pttl.mobile.manager.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReaderBuilder;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.BpmIdMappingPageDTO;
import com.pttl.mobile.manager.domain.entity.BpmIdMapping;
import com.pttl.mobile.manager.domain.request.BpmIdMappingPageRequest;
import com.pttl.mobile.manager.domain.request.BpmIdMappingSaveRequest;
import com.pttl.mobile.manager.domain.request.BpmIdMappingUpdateRequest;
import com.pttl.mobile.manager.excel.dto.ExportBpmIdMappingDataDTO;
import com.pttl.mobile.manager.excel.dto.ImportBpmIdMappingDataDTO;
import com.pttl.mobile.manager.excel.exception.ExcelErrorResultException;
import com.pttl.mobile.manager.excel.listener.ImportBpmIdMappingDataListener;
import com.pttl.mobile.manager.excel.util.ExportExcelFileUtil;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.BpmIdMappingService;
import com.pttl.mobile.manager.util.BeanMapper;
import com.pttl.mobile.manager.util.FileDownloadUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * BPM ID映射控制层
 *
 * <AUTHOR>
 * @date 2025/6/17
 */
@Slf4j
@RestController
@RequestMapping("/bpmIdMapping")
@Api(tags = "BPM ID映射管理")
@Validated
public class BpmIdMappingController {

    @Autowired
    private BpmIdMappingService bpmIdMappingService;

    @Value("${excelFile.bpmIdMappingTemplatePath:/home/<USER>/xiBel/manager/bpmIdMappingTemplate.xlsx}")
    private String excelTemplatePath;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询BPM映射", notes = "根据ID查询BPM映射详情")
    public ResponseMessage<BpmIdMapping> selectOne(@PathVariable("id") Long id) {
        BpmIdMapping result = bpmIdMappingService.selectByPrimaryKey(id);
        return ResponseMessage.ok(result);
    }
